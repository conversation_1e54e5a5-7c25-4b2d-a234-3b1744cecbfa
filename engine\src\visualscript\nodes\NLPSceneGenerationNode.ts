/**
 * 自然语言场景生成节点
 */
import { AsyncNode } from './AsyncNode';
import { NodeCategory, SocketType } from './Node';
import { GenerationOptions } from '../../ai/NLPSceneGenerator';

export class NLPSceneGenerationNode extends AsyncNode {
  constructor(options: any) {
    super({
      id: options.id,
      type: 'nlp/scene/generate',
      metadata: options.metadata,
      graph: options.graph,
      context: options.context
    });

    // 设置节点类别
    (this as any).category = NodeCategory.AI;

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      description: '场景描述文本',
      optional: false
    });

    this.addInput({
      name: 'style',
      type: SocketType.DATA,
      dataType: 'string',
      description: '生成风格',
      defaultValue: 'realistic',
      optional: true
    });

    this.addInput({
      name: 'quality',
      type: SocketType.DATA,
      dataType: 'number',
      description: '质量等级 (1-100)',
      defaultValue: 80,
      optional: true
    });

    this.addInput({
      name: 'maxObjects',
      type: SocketType.DATA,
      dataType: 'number',
      description: '最大对象数',
      defaultValue: 50,
      optional: true
    });

    this.addInput({
      name: 'customStyle',
      type: SocketType.DATA,
      dataType: 'object',
      description: '自定义风格配置',
      optional: true
    });

    this.addInput({
      name: 'customObjects',
      type: SocketType.DATA,
      dataType: 'array',
      description: '自定义对象类型列表',
      optional: true
    });

    this.addInput({
      name: 'aiServices',
      type: SocketType.DATA,
      dataType: 'array',
      description: 'AI服务列表',
      optional: true
    });

    this.addInput({
      name: 'enableAdvancedFeatures',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '启用高级功能',
      defaultValue: false,
      optional: true
    });

    this.addInput({
      name: 'seedValue',
      type: SocketType.DATA,
      dataType: 'number',
      description: '随机种子值',
      optional: true
    });

    this.addInput({
      name: 'templateScene',
      type: SocketType.DATA,
      dataType: 'string',
      description: '模板场景ID',
      optional: true
    });

    // 输出插槽
    this.addOutput({
      name: 'scene',
      type: SocketType.DATA,
      dataType: 'object',
      description: '生成的场景'
    });

    this.addOutput({
      name: 'understanding',
      type: SocketType.DATA,
      dataType: 'object',
      description: '语言理解结果'
    });

    this.addOutput({
      name: 'progress',
      type: SocketType.DATA,
      dataType: 'number',
      description: '生成进度 (0-100)'
    });

    this.addOutput({
      name: 'metadata',
      type: SocketType.DATA,
      dataType: 'object',
      description: '生成元数据'
    });

    // 流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      description: '生成成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      description: '生成失败'
    });

    this.addOutput({
      name: 'progress_update',
      type: SocketType.FLOW,
      description: '进度更新'
    });
  }

  protected async executeAsyncImpl(): Promise<any> {
    const text = this.getInputValue('text') as string;
    const style = this.getInputValue('style') as string || 'realistic';
    const quality = this.getInputValue('quality') as number || 80;
    const maxObjects = this.getInputValue('maxObjects') as number || 50;
    const customStyle = this.getInputValue('customStyle') as any;
    const customObjects = this.getInputValue('customObjects') as any[];
    const aiServices = this.getInputValue('aiServices') as string[];
    const enableAdvancedFeatures = this.getInputValue('enableAdvancedFeatures') as boolean || false;
    const seedValue = this.getInputValue('seedValue') as number;
    const templateScene = this.getInputValue('templateScene') as string;

    // 验证输入
    if (!text || text.trim().length === 0) {
      throw new Error('场景描述不能为空');
    }

    const world = this.context.world;
    const nlpGenerator = (world as any).getSystem('NLPSceneGenerator');

    if (!nlpGenerator) {
      throw new Error('自然语言场景生成器未初始化');
    }

    const options: GenerationOptions = {
      style: style as any,
      quality,
      maxObjects,
      constraints: {
        maxPolygons: quality * 1000,
        targetFrameRate: 60
      },
      onProgress: (progress: number) => {
        this.setOutputValue('progress', progress);
        this.triggerFlow('progress_update');
      },
      customStyle,
      customObjects,
      aiServices,
      enableAdvancedFeatures,
      seedValue,
      templateScene
    };

    // 开始生成
    this.setOutputValue('progress', 0);

    const scene = await (nlpGenerator as any).generateSceneFromNaturalLanguage(text, options);

    // 设置输出值
    this.setOutputValue('scene', scene);
    this.setOutputValue('progress', 100);

    // 如果有理解结果，也输出
    if ((scene as any).understanding) {
      this.setOutputValue('understanding', (scene as any).understanding);
    }

    // 设置增强的元数据
    this.setOutputValue('metadata', {
      generationTime: Date.now(),
      objectCount: (scene as any).entities?.length || 0,
      style,
      quality,
      customFeaturesUsed: {
        customStyle: !!customStyle,
        customObjects: !!customObjects,
        aiServices: aiServices?.length || 0,
        advancedFeatures: enableAdvancedFeatures,
        seedValue: !!seedValue,
        templateScene: !!templateScene
      }
    });

    return { success: true, scene };
  }

  // 节点配置
  getConfiguration(): any {
    return {
      color: '#722ED1',
      icon: 'robot',
      category: 'AI',
      tags: ['nlp', 'scene', 'generation', 'ai'],
      examples: [
        {
          name: '基础场景生成',
          description: '使用简单描述生成场景',
          inputs: {
            text: '创建一个现代办公室',
            style: 'realistic',
            quality: 70,
            maxObjects: 30
          }
        },
        {
          name: '复杂场景生成',
          description: '使用详细描述生成复杂场景',
          inputs: {
            text: '创建一个温馨的咖啡厅，有木质桌椅、暖色灯光和绿色植物',
            style: 'realistic',
            quality: 90,
            maxObjects: 60
          }
        },
        {
          name: '自定义风格场景',
          description: '使用自定义风格生成场景',
          inputs: {
            text: '创建一个工业风格的工厂车间',
            style: 'industrial',
            quality: 85,
            maxObjects: 40,
            enableAdvancedFeatures: true
          }
        },
        {
          name: 'AI增强生成',
          description: '使用AI服务增强场景生成',
          inputs: {
            text: '创建一个科幻实验室，要有未来感和高科技设备',
            style: 'scifi',
            quality: 95,
            maxObjects: 50,
            aiServices: ['openai_gpt4'],
            enableAdvancedFeatures: true
          }
        },
        {
          name: '可重现生成',
          description: '使用固定种子生成可重现的场景',
          inputs: {
            text: '创建一个魔法森林',
            style: 'fantasy',
            quality: 80,
            maxObjects: 45,
            seedValue: 12345,
            enableAdvancedFeatures: true
          }
        }
      ]
    };
  }
}
