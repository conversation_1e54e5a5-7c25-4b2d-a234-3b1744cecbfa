/**
 * 场景理解节点
 */
import { AsyncNode } from './AsyncNode';
import { NodeCategory, SocketType } from './Node';

export class SceneUnderstandingNode extends AsyncNode {
  constructor(options: any) {
    super({
      id: options.id,
      type: 'nlp/scene/understand',
      metadata: options.metadata,
      graph: options.graph,
      context: options.context
    });

    // 设置节点类别
    (this as any).category = NodeCategory.AI;

    this.setupSockets();
  }

  private setupSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'text',
      type: SocketType.DATA,
      dataType: 'string',
      description: '输入文本',
      optional: false
    });

    // 输出插槽
    this.addOutput({
      name: 'entities',
      type: SocketType.DATA,
      dataType: 'array',
      description: '识别的实体'
    });

    this.addOutput({
      name: 'sentiment',
      type: SocketType.DATA,
      dataType: 'string',
      description: '情感倾向'
    });

    this.addOutput({
      name: 'intent',
      type: SocketType.DATA,
      dataType: 'string',
      description: '意图'
    });

    this.addOutput({
      name: 'keywords',
      type: SocketType.DATA,
      dataType: 'array',
      description: '关键词'
    });

    this.addOutput({
      name: 'style',
      type: SocketType.DATA,
      dataType: 'string',
      description: '推断风格'
    });

    // 流程插槽
    this.addOutput({
      name: 'success',
      type: SocketType.FLOW,
      description: '理解成功'
    });

    this.addOutput({
      name: 'error',
      type: SocketType.FLOW,
      description: '理解失败'
    });
  }

  protected async executeAsyncImpl(_inputs: Record<string, any>): Promise<any> {
    const text = this.getInputValue('text') as string;

    if (!text || text.trim().length === 0) {
      throw new Error('输入文本不能为空');
    }

    const world = this.context.world;
    const nlpGenerator = (world as any).getSystem('NLPSceneGenerator');

    if (!nlpGenerator) {
      throw new Error('自然语言场景生成器未初始化');
    }

    // 调用理解方法（需要在NLPSceneGenerator中暴露）
    const understanding = await (nlpGenerator as any).understandText(text);

    // 设置输出值
    this.setOutputValue('entities', understanding.entities);
    this.setOutputValue('sentiment', understanding.sentiment);
    this.setOutputValue('intent', understanding.intent);
    this.setOutputValue('keywords', understanding.keywords);
    this.setOutputValue('style', understanding.style);

    return { success: true, understanding };
  }

  getConfiguration(): any {
    return {
      color: '#52C41A',
      icon: 'eye',
      category: 'AI',
      tags: ['nlp', 'understanding', 'analysis'],
      examples: [
        {
          name: '基础文本理解',
          description: '理解简单的场景描述',
          inputs: {
            text: '创建一个现代办公室'
          }
        },
        {
          name: '复杂文本理解',
          description: '理解详细的场景描述',
          inputs: {
            text: '设计一个温馨舒适的咖啡厅，有木质桌椅和绿色植物'
          }
        }
      ]
    };
  }
}
