/**
 * 优化的执行引擎
 * 提供高性能的节点执行、缓存和内存管理
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Node } from '../nodes/Node';
import { Graph } from '../graph/Graph';
import { ExecutionContext } from '../execution/ExecutionContext';

// 执行优化选项
interface OptimizationOptions {
  enableNodeCaching: boolean;
  enableLazyEvaluation: boolean;
  enableParallelExecution: boolean;
  maxCacheSize: number;
  maxExecutionTime: number;
  memoryThreshold: number;
  enableProfiling: boolean;
  maxRetryAttempts: number;
  retryDelay: number;
  enableCircularDependencyCheck: boolean;
  maxParallelWorkers: number;
}

// 节点缓存项
interface NodeCacheItem {
  nodeId: string;
  inputHash: string;
  output: any;
  timestamp: number;
  accessCount: number;
  executionTime: number;
}

// 执行统计
interface ExecutionStats {
  totalNodes: number;
  executedNodes: number;
  cachedNodes: number;
  parallelNodes: number;
  totalExecutionTime: number;
  averageExecutionTime: number;
  memoryUsage: number;
  cacheHitRate: number;
  failedNodes: number;
  retriedNodes: number;
  timeoutNodes: number;
}

// 并行任务接口
interface ParallelTask {
  node: Node;
  context: ExecutionContext;
  resolve: (value: any) => void;
  reject: (error: any) => void;
  startTime: number;
  retryCount: number;
}

// 执行结果接口
interface ExecutionResult {
  success: boolean;
  result?: any;
  error?: Error;
  executionTime: number;
  retryCount: number;
}

// 性能分析器
class PerformanceProfiler {
  private profiles: Map<string, any> = new Map();
  private enabled: boolean = false;

  enable(): void {
    this.enabled = true;
  }

  disable(): void {
    this.enabled = false;
  }

  startProfile(nodeId: string): void {
    if (!this.enabled) return;
    
    this.profiles.set(nodeId, {
      startTime: performance.now(),
      memoryBefore: this.getMemoryUsage()
    });
  }

  endProfile(nodeId: string): any {
    if (!this.enabled) return null;
    
    const profile = this.profiles.get(nodeId);
    if (!profile) return null;

    const endTime = performance.now();
    const memoryAfter = this.getMemoryUsage();

    const result = {
      nodeId,
      executionTime: endTime - profile.startTime,
      memoryDelta: memoryAfter - profile.memoryBefore,
      timestamp: Date.now()
    };

    this.profiles.delete(nodeId);
    return result;
  }

  private getMemoryUsage(): number {
    // 在浏览器环境中使用 performance.memory
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    
    // 在 Node.js 环境中使用 process.memoryUsage
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    
    return 0;
  }

  getProfiles(): any[] {
    return Array.from(this.profiles.values());
  }

  clearProfiles(): void {
    this.profiles.clear();
  }
}

/**
 * 优化的执行引擎
 */
export class OptimizedExecutionEngine extends EventEmitter {
  private options: OptimizationOptions;
  private nodeCache: Map<string, NodeCacheItem> = new Map();
  private executionQueue: Node[] = [];
  private parallelTasks: Map<string, ParallelTask> = new Map();
  private profiler: PerformanceProfiler = new PerformanceProfiler();
  private cleanupTimer: NodeJS.Timeout | null = null;
  private stats: ExecutionStats = {
    totalNodes: 0,
    executedNodes: 0,
    cachedNodes: 0,
    parallelNodes: 0,
    totalExecutionTime: 0,
    averageExecutionTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0,
    failedNodes: 0,
    retriedNodes: 0,
    timeoutNodes: 0
  };

  constructor(options: Partial<OptimizationOptions> = {}) {
    super();
    
    this.options = {
      enableNodeCaching: true,
      enableLazyEvaluation: true,
      enableParallelExecution: false, // 默认关闭并行执行
      maxCacheSize: 1000,
      maxExecutionTime: 30000,
      memoryThreshold: 100 * 1024 * 1024, // 100MB
      enableProfiling: false,
      maxRetryAttempts: 3,
      retryDelay: 1000,
      enableCircularDependencyCheck: true,
      maxParallelWorkers: 4,
      ...options
    };

    if (this.options.enableProfiling) {
      this.profiler.enable();
    }

    // 定期清理缓存
    this.cleanupTimer = setInterval(() => this.cleanupCache(), 60000);
  }

  /**
   * 执行图形
   */
  async executeGraph(graph: Graph, context: ExecutionContext): Promise<any> {
    const startTime = performance.now();
    
    try {
      // 重置统计
      this.resetStats();
      this.stats.totalNodes = graph.getNodes().length;

      // 检查内存使用
      if (this.isMemoryThresholdExceeded()) {
        this.emit('memoryWarning', { usage: this.getCurrentMemoryUsage() });
        await this.performGarbageCollection();
      }

      // 构建执行计划
      const executionPlan = this.buildExecutionPlan(graph);
      
      // 执行节点
      const result = await this.executeNodes(executionPlan, context);
      
      // 更新统计
      const endTime = performance.now();
      this.stats.totalExecutionTime = endTime - startTime;
      this.stats.averageExecutionTime = this.stats.totalExecutionTime / this.stats.executedNodes;
      this.stats.memoryUsage = this.getCurrentMemoryUsage();
      this.stats.cacheHitRate = this.stats.cachedNodes / this.stats.totalNodes;

      this.emit('executionCompleted', { result, stats: this.stats });
      return result;

    } catch (error) {
      this.emit('executionError', { error, stats: this.stats });
      throw error;
    }
  }

  /**
   * 构建执行计划
   */
  private buildExecutionPlan(graph: Graph): Node[] {
    const nodes = graph.getNodes();

    // 检查循环依赖
    if (this.options.enableCircularDependencyCheck) {
      this.checkCircularDependencies(nodes);
    }

    const visited = new Set<string>();
    const visiting = new Set<string>();
    const plan: Node[] = [];

    // 拓扑排序
    const topologicalSort = (node: Node) => {
      if (visited.has(node.id)) return;

      if (visiting.has(node.id)) {
        throw new Error(`检测到循环依赖: 节点 ${node.id}`);
      }

      visiting.add(node.id);

      // 先处理依赖节点
      const dependencies = this.getNodeDependencies(node, graph);
      for (const dep of dependencies) {
        topologicalSort(dep);
      }

      visiting.delete(node.id);
      visited.add(node.id);
      plan.push(node);
    };

    // 从入口节点开始
    const entryNodes = this.findEntryNodes(nodes, graph);
    for (const node of entryNodes) {
      topologicalSort(node);
    }

    return plan;
  }

  /**
   * 执行节点列表
   */
  private async executeNodes(nodes: Node[], context: ExecutionContext): Promise<any> {
    if (this.options.enableParallelExecution && nodes.length > 1) {
      return this.executeNodesInParallel(nodes, context);
    } else {
      return this.executeNodesSequentially(nodes, context);
    }
  }

  /**
   * 顺序执行节点
   */
  private async executeNodesSequentially(nodes: Node[], context: ExecutionContext): Promise<any> {
    const results: Map<string, any> = new Map();

    for (const node of nodes) {
      try {
        const result = await this.executeNode(node, context);
        results.set(node.id, result);
        this.stats.executedNodes++;
      } catch (error) {
        this.emit('nodeError', { nodeId: node.id, error });
        throw error;
      }
    }

    return Object.fromEntries(results);
  }

  /**
   * 并行执行节点
   */
  private async executeNodesInParallel(nodes: Node[], context: ExecutionContext): Promise<any> {
    const results: Map<string, any> = new Map();
    const batches = this.createExecutionBatches(nodes);

    for (const batch of batches) {
      const batchPromises = batch.map(async (node) => {
        try {
          const result = await this.executeNode(node, context);
          results.set(node.id, result);
          this.stats.executedNodes++;
          this.stats.parallelNodes++;
          return { nodeId: node.id, result };
        } catch (error) {
          this.emit('nodeError', { nodeId: node.id, error });
          throw error;
        }
      });

      await Promise.all(batchPromises);
    }

    return Object.fromEntries(results);
  }

  /**
   * 创建执行批次（确保依赖关系正确）
   */
  private createExecutionBatches(nodes: Node[]): Node[][] {
    const batches: Node[][] = [];
    const processed = new Set<string>();
    const remaining = [...nodes];

    while (remaining.length > 0) {
      const currentBatch: Node[] = [];

      // 找到所有依赖已满足的节点
      for (let i = remaining.length - 1; i >= 0; i--) {
        const node = remaining[i];
        const dependencies = node.getDependencies();
        const dependenciesSatisfied = dependencies.every(dep => processed.has(dep.id));

        if (dependenciesSatisfied) {
          currentBatch.push(node);
          remaining.splice(i, 1);
        }
      }

      if (currentBatch.length === 0) {
        throw new Error('无法解析节点依赖关系，可能存在循环依赖');
      }

      // 限制并行度
      const maxBatchSize = Math.min(currentBatch.length, this.options.maxParallelWorkers);
      while (currentBatch.length > 0) {
        const batch = currentBatch.splice(0, maxBatchSize);
        batches.push(batch);
        batch.forEach(node => processed.add(node.id));
      }
    }

    return batches;
  }

  /**
   * 执行单个节点
   */
  private async executeNode(node: Node, _context: ExecutionContext): Promise<any> {
    return this.executeNodeWithRetry(node, 0);
  }

  /**
   * 带重试机制的节点执行
   */
  private async executeNodeWithRetry(node: Node, retryCount: number): Promise<any> {
    // 检查缓存
    if (this.options.enableNodeCaching) {
      const cached = this.getCachedResult(node);
      if (cached) {
        this.stats.cachedNodes++;
        return cached.output;
      }
    }

    // 开始性能分析
    this.profiler.startProfile(node.id);
    const startTime = performance.now();

    try {
      // 懒加载检查
      if (this.options.enableLazyEvaluation && !this.shouldExecuteNode(node)) {
        return null;
      }

      // 执行节点（带超时控制）
      const result = await this.executeWithTimeout(node);

      // 更新缓存中的执行时间
      const executionTime = performance.now() - startTime;
      if (this.options.enableNodeCaching && this.shouldCacheResult(node, result)) {
        this.cacheResult(node, result);
        // 更新缓存项的执行时间
        const inputHash = this.calculateInputHash(node);
        const cacheKey = `${node.id}_${inputHash}`;
        const cached = this.nodeCache.get(cacheKey);
        if (cached) {
          cached.executionTime = executionTime;
        }
      }

      // 结束性能分析
      const profile = this.profiler.endProfile(node.id);
      if (profile) {
        this.emit('nodeProfiled', profile);
      }

      return result;

    } catch (error) {
      this.profiler.endProfile(node.id);

      // 重试逻辑
      if (retryCount < this.options.maxRetryAttempts) {
        this.stats.retriedNodes++;
        this.emit('nodeRetry', { nodeId: node.id, retryCount: retryCount + 1, error });

        // 等待重试延迟
        await new Promise(resolve => setTimeout(resolve, this.options.retryDelay));

        return this.executeNodeWithRetry(node, retryCount + 1);
      }

      this.stats.failedNodes++;
      throw error;
    }
  }

  /**
   * 获取缓存结果
   */
  private getCachedResult(node: Node): NodeCacheItem | null {
    const inputHash = this.calculateInputHash(node);
    const cacheKey = `${node.id}_${inputHash}`;
    const cached = this.nodeCache.get(cacheKey);

    if (cached && this.isCacheValid(cached)) {
      cached.accessCount++;
      return cached;
    }

    return null;
  }

  /**
   * 缓存结果
   */
  private cacheResult(node: Node, result: any): void {
    if (this.nodeCache.size >= this.options.maxCacheSize) {
      this.evictLeastUsedCache();
    }

    const inputHash = this.calculateInputHash(node);
    const cacheKey = `${node.id}_${inputHash}`;

    this.nodeCache.set(cacheKey, {
      nodeId: node.id,
      inputHash,
      output: result,
      timestamp: Date.now(),
      accessCount: 1,
      executionTime: 0 // 将在性能分析中更新
    });
  }

  /**
   * 计算输入哈希
   */
  private calculateInputHash(node: Node): string {
    // 简化的哈希计算，实际应该更复杂
    const inputs: Record<string, any> = {};
    for (const [name, socket] of node.getInputs().entries()) {
      inputs[name] = socket.value;
    }
    return JSON.stringify(inputs);
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cached: NodeCacheItem): boolean {
    const maxAge = 5 * 60 * 1000; // 5分钟
    return Date.now() - cached.timestamp < maxAge;
  }

  /**
   * 驱逐最少使用的缓存
   */
  private evictLeastUsedCache(): void {
    let leastUsed: string | null = null;
    let minAccessCount = Infinity;

    for (const [key, item] of this.nodeCache.entries()) {
      if (item.accessCount < minAccessCount) {
        minAccessCount = item.accessCount;
        leastUsed = key;
      }
    }

    if (leastUsed) {
      this.nodeCache.delete(leastUsed);
    }
  }

  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10分钟

    for (const [key, item] of this.nodeCache.entries()) {
      if (now - item.timestamp > maxAge) {
        this.nodeCache.delete(key);
      }
    }

    this.emit('cacheCleanup', { 
      remainingItems: this.nodeCache.size,
      maxSize: this.options.maxCacheSize
    });
  }

  /**
   * 检查是否应该执行节点
   */
  private shouldExecuteNode(node: Node): boolean {
    // 懒加载逻辑：只有当节点的输出被需要时才执行
    // 检查是否有输出连接
    for (const [, connections] of (node as any).outputConnections.entries()) {
      if (connections.length > 0) {
        return true;
      }
    }
    return false;
  }

  /**
   * 检查是否应该缓存结果
   */
  private shouldCacheResult(_node: Node, result: any): boolean {
    // 不缓存空结果或错误结果
    if (!result || result instanceof Error) {
      return false;
    }

    // 不缓存过大的结果
    const resultSize = JSON.stringify(result).length;
    if (resultSize > 1024 * 1024) { // 1MB
      return false;
    }

    return true;
  }

  /**
   * 获取节点依赖
   */
  private getNodeDependencies(node: Node, _graph: Graph): Node[] {
    // 使用Node类的getDependencies方法
    return node.getDependencies();
  }

  /**
   * 查找入口节点
   */
  private findEntryNodes(nodes: Node[], _graph: Graph): Node[] {
    const entryNodes: Node[] = [];

    for (const node of nodes) {
      // 检查节点是否有输入连接，如果没有则为入口节点
      const dependencies = node.getDependencies();
      if (dependencies.length === 0) {
        entryNodes.push(node);
      }
    }

    return entryNodes;
  }

  /**
   * 检查循环依赖
   */
  private checkCircularDependencies(nodes: Node[]): void {
    const visited = new Set<string>();
    const visiting = new Set<string>();

    const dfs = (node: Node): void => {
      if (visited.has(node.id)) return;

      if (visiting.has(node.id)) {
        throw new Error(`检测到循环依赖: 节点 ${node.id}`);
      }

      visiting.add(node.id);

      const dependencies = node.getDependencies();
      for (const dep of dependencies) {
        dfs(dep);
      }

      visiting.delete(node.id);
      visited.add(node.id);
    };

    for (const node of nodes) {
      if (!visited.has(node.id)) {
        dfs(node);
      }
    }
  }

  /**
   * 带超时的节点执行
   */
  private async executeWithTimeout(node: Node): Promise<any> {
    return new Promise((resolve, reject) => {
      const timeoutId = setTimeout(() => {
        this.stats.timeoutNodes++;
        reject(new Error(`节点执行超时: ${node.id} (${this.options.maxExecutionTime}ms)`));
      }, this.options.maxExecutionTime);

      node.execute()
        .then(result => {
          clearTimeout(timeoutId);
          resolve(result);
        })
        .catch(error => {
          clearTimeout(timeoutId);
          reject(error);
        });
    });
  }

  /**
   * 检查内存阈值
   */
  private isMemoryThresholdExceeded(): boolean {
    const currentUsage = this.getCurrentMemoryUsage();
    return currentUsage > this.options.memoryThreshold;
  }

  /**
   * 获取当前内存使用
   */
  private getCurrentMemoryUsage(): number {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    
    return 0;
  }

  /**
   * 执行垃圾回收
   */
  private async performGarbageCollection(): Promise<void> {
    // 清理缓存
    this.nodeCache.clear();
    
    // 清理性能分析数据
    this.profiler.clearProfiles();
    
    // 触发垃圾回收（如果可用）
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    }

    this.emit('garbageCollected', { 
      memoryUsage: this.getCurrentMemoryUsage()
    });
  }

  /**
   * 重置统计
   */
  private resetStats(): void {
    this.stats = {
      totalNodes: 0,
      executedNodes: 0,
      cachedNodes: 0,
      parallelNodes: 0,
      totalExecutionTime: 0,
      averageExecutionTime: 0,
      memoryUsage: 0,
      cacheHitRate: 0,
      failedNodes: 0,
      retriedNodes: 0,
      timeoutNodes: 0
    };
  }

  /**
   * 获取执行统计
   */
  getStats(): ExecutionStats {
    return { ...this.stats };
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): any {
    return {
      size: this.nodeCache.size,
      maxSize: this.options.maxCacheSize,
      hitRate: this.stats.cacheHitRate,
      items: Array.from(this.nodeCache.values()).map(item => ({
        nodeId: item.nodeId,
        accessCount: item.accessCount,
        age: Date.now() - item.timestamp
      }))
    };
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.nodeCache.clear();
    this.emit('cacheCleared');
  }

  /**
   * 更新优化选项
   */
  updateOptions(options: Partial<OptimizationOptions>): void {
    this.options = { ...this.options, ...options };
    
    if (options.enableProfiling !== undefined) {
      if (options.enableProfiling) {
        this.profiler.enable();
      } else {
        this.profiler.disable();
      }
    }
  }

  /**
   * 销毁引擎
   */
  destroy(): void {
    // 清理定时器
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
    }

    // 清理缓存和数据
    this.clearCache();
    this.profiler.clearProfiles();
    this.parallelTasks.clear();
    this.executionQueue.length = 0;

    // 移除所有事件监听器
    this.removeAllListeners();
  }

  /**
   * 暂停执行
   */
  pause(): void {
    this.emit('executionPaused');
  }

  /**
   * 恢复执行
   */
  resume(): void {
    this.emit('executionResumed');
  }

  /**
   * 获取详细的性能报告
   */
  getPerformanceReport(): any {
    return {
      stats: this.getStats(),
      cacheStats: this.getCacheStats(),
      profilerData: this.profiler.getProfiles(),
      memoryUsage: this.getCurrentMemoryUsage(),
      options: this.options,
      timestamp: Date.now()
    };
  }

  /**
   * 预热缓存（预执行一些节点以填充缓存）
   */
  async warmupCache(graph: Graph, context: ExecutionContext): Promise<void> {
    const nodes = graph.getNodes();
    const sampleNodes = nodes.slice(0, Math.min(10, nodes.length)); // 预热前10个节点

    for (const node of sampleNodes) {
      try {
        await this.executeNode(node, context);
      } catch (error) {
        // 忽略预热过程中的错误
        console.warn(`预热节点 ${node.id} 失败:`, error);
      }
    }

    this.emit('cacheWarmedUp', { nodesWarmed: sampleNodes.length });
  }

  /**
   * 获取执行队列状态
   */
  getExecutionQueueStatus(): any {
    return {
      queueLength: this.executionQueue.length,
      parallelTasks: this.parallelTasks.size,
      isExecuting: this.parallelTasks.size > 0
    };
  }
}
