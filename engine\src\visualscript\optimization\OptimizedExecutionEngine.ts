/**
 * 优化的执行引擎
 * 提供高性能的节点执行、缓存和内存管理
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { Node } from '../nodes/Node';
import { Graph } from '../graph/Graph';
import { ExecutionContext } from '../execution/ExecutionContext';

// 执行优化选项
interface OptimizationOptions {
  enableNodeCaching: boolean;
  enableLazyEvaluation: boolean;
  enableParallelExecution: boolean;
  maxCacheSize: number;
  maxExecutionTime: number;
  memoryThreshold: number;
  enableProfiling: boolean;
}

// 节点缓存项
interface NodeCacheItem {
  nodeId: string;
  inputHash: string;
  output: any;
  timestamp: number;
  accessCount: number;
  executionTime: number;
}

// 执行统计
interface ExecutionStats {
  totalNodes: number;
  executedNodes: number;
  cachedNodes: number;
  parallelNodes: number;
  totalExecutionTime: number;
  averageExecutionTime: number;
  memoryUsage: number;
  cacheHitRate: number;
}

// 性能分析器
class PerformanceProfiler {
  private profiles: Map<string, any> = new Map();
  private enabled: boolean = false;

  enable(): void {
    this.enabled = true;
  }

  disable(): void {
    this.enabled = false;
  }

  startProfile(nodeId: string): void {
    if (!this.enabled) return;
    
    this.profiles.set(nodeId, {
      startTime: performance.now(),
      memoryBefore: this.getMemoryUsage()
    });
  }

  endProfile(nodeId: string): any {
    if (!this.enabled) return null;
    
    const profile = this.profiles.get(nodeId);
    if (!profile) return null;

    const endTime = performance.now();
    const memoryAfter = this.getMemoryUsage();

    const result = {
      nodeId,
      executionTime: endTime - profile.startTime,
      memoryDelta: memoryAfter - profile.memoryBefore,
      timestamp: Date.now()
    };

    this.profiles.delete(nodeId);
    return result;
  }

  private getMemoryUsage(): number {
    // 在浏览器环境中使用 performance.memory
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    
    // 在 Node.js 环境中使用 process.memoryUsage
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    
    return 0;
  }

  getProfiles(): any[] {
    return Array.from(this.profiles.values());
  }

  clearProfiles(): void {
    this.profiles.clear();
  }
}

/**
 * 优化的执行引擎
 */
export class OptimizedExecutionEngine extends EventEmitter {
  private options: OptimizationOptions;
  private nodeCache: Map<string, NodeCacheItem> = new Map();
  private executionQueue: Node[] = [];
  private parallelExecutors: Worker[] = [];
  private profiler: PerformanceProfiler = new PerformanceProfiler();
  private stats: ExecutionStats = {
    totalNodes: 0,
    executedNodes: 0,
    cachedNodes: 0,
    parallelNodes: 0,
    totalExecutionTime: 0,
    averageExecutionTime: 0,
    memoryUsage: 0,
    cacheHitRate: 0
  };

  constructor(options: Partial<OptimizationOptions> = {}) {
    super();
    
    this.options = {
      enableNodeCaching: true,
      enableLazyEvaluation: true,
      enableParallelExecution: false, // 默认关闭并行执行
      maxCacheSize: 1000,
      maxExecutionTime: 30000,
      memoryThreshold: 100 * 1024 * 1024, // 100MB
      enableProfiling: false,
      ...options
    };

    if (this.options.enableProfiling) {
      this.profiler.enable();
    }

    // 定期清理缓存
    setInterval(() => this.cleanupCache(), 60000);
  }

  /**
   * 执行图形
   */
  async executeGraph(graph: Graph, context: ExecutionContext): Promise<any> {
    const startTime = performance.now();
    
    try {
      // 重置统计
      this.resetStats();
      this.stats.totalNodes = graph.getNodes().length;

      // 检查内存使用
      if (this.isMemoryThresholdExceeded()) {
        this.emit('memoryWarning', { usage: this.getCurrentMemoryUsage() });
        await this.performGarbageCollection();
      }

      // 构建执行计划
      const executionPlan = this.buildExecutionPlan(graph);
      
      // 执行节点
      const result = await this.executeNodes(executionPlan, context);
      
      // 更新统计
      const endTime = performance.now();
      this.stats.totalExecutionTime = endTime - startTime;
      this.stats.averageExecutionTime = this.stats.totalExecutionTime / this.stats.executedNodes;
      this.stats.memoryUsage = this.getCurrentMemoryUsage();
      this.stats.cacheHitRate = this.stats.cachedNodes / this.stats.totalNodes;

      this.emit('executionCompleted', { result, stats: this.stats });
      return result;

    } catch (error) {
      this.emit('executionError', { error, stats: this.stats });
      throw error;
    }
  }

  /**
   * 构建执行计划
   */
  private buildExecutionPlan(graph: Graph): Node[] {
    const nodes = graph.getNodes();
    const visited = new Set<string>();
    const plan: Node[] = [];

    // 拓扑排序
    const topologicalSort = (node: Node) => {
      if (visited.has(node.id)) return;
      visited.add(node.id);

      // 先处理依赖节点
      const dependencies = this.getNodeDependencies(node, graph);
      for (const dep of dependencies) {
        topologicalSort(dep);
      }

      plan.push(node);
    };

    // 从入口节点开始
    const entryNodes = this.findEntryNodes(nodes, graph);
    for (const node of entryNodes) {
      topologicalSort(node);
    }

    return plan;
  }

  /**
   * 执行节点列表
   */
  private async executeNodes(nodes: Node[], context: ExecutionContext): Promise<any> {
    const results: Map<string, any> = new Map();

    for (const node of nodes) {
      try {
        const result = await this.executeNode(node, context);
        results.set(node.id, result);
        this.stats.executedNodes++;
      } catch (error) {
        this.emit('nodeError', { nodeId: node.id, error });
        throw error;
      }
    }

    return Object.fromEntries(results);
  }

  /**
   * 执行单个节点
   */
  private async executeNode(node: Node, context: ExecutionContext): Promise<any> {
    // 检查缓存
    if (this.options.enableNodeCaching) {
      const cached = this.getCachedResult(node);
      if (cached) {
        this.stats.cachedNodes++;
        return cached.output;
      }
    }

    // 开始性能分析
    this.profiler.startProfile(node.id);

    try {
      // 懒加载检查
      if (this.options.enableLazyEvaluation && !this.shouldExecuteNode(node)) {
        return null;
      }

      // 执行节点
      const result = await node.execute();

      // 缓存结果
      if (this.options.enableNodeCaching && this.shouldCacheResult(node, result)) {
        this.cacheResult(node, result);
      }

      // 结束性能分析
      const profile = this.profiler.endProfile(node.id);
      if (profile) {
        this.emit('nodeProfiled', profile);
      }

      return result;

    } catch (error) {
      this.profiler.endProfile(node.id);
      throw error;
    }
  }

  /**
   * 获取缓存结果
   */
  private getCachedResult(node: Node): NodeCacheItem | null {
    const inputHash = this.calculateInputHash(node);
    const cacheKey = `${node.id}_${inputHash}`;
    const cached = this.nodeCache.get(cacheKey);

    if (cached && this.isCacheValid(cached)) {
      cached.accessCount++;
      return cached;
    }

    return null;
  }

  /**
   * 缓存结果
   */
  private cacheResult(node: Node, result: any): void {
    if (this.nodeCache.size >= this.options.maxCacheSize) {
      this.evictLeastUsedCache();
    }

    const inputHash = this.calculateInputHash(node);
    const cacheKey = `${node.id}_${inputHash}`;

    this.nodeCache.set(cacheKey, {
      nodeId: node.id,
      inputHash,
      output: result,
      timestamp: Date.now(),
      accessCount: 1,
      executionTime: 0 // 将在性能分析中更新
    });
  }

  /**
   * 计算输入哈希
   */
  private calculateInputHash(node: Node): string {
    // 简化的哈希计算，实际应该更复杂
    const inputs: Record<string, any> = {};
    for (const [name, socket] of node.getInputs().entries()) {
      inputs[name] = socket.value;
    }
    return JSON.stringify(inputs);
  }

  /**
   * 检查缓存是否有效
   */
  private isCacheValid(cached: NodeCacheItem): boolean {
    const maxAge = 5 * 60 * 1000; // 5分钟
    return Date.now() - cached.timestamp < maxAge;
  }

  /**
   * 驱逐最少使用的缓存
   */
  private evictLeastUsedCache(): void {
    let leastUsed: string | null = null;
    let minAccessCount = Infinity;

    for (const [key, item] of this.nodeCache.entries()) {
      if (item.accessCount < minAccessCount) {
        minAccessCount = item.accessCount;
        leastUsed = key;
      }
    }

    if (leastUsed) {
      this.nodeCache.delete(leastUsed);
    }
  }

  /**
   * 清理缓存
   */
  private cleanupCache(): void {
    const now = Date.now();
    const maxAge = 10 * 60 * 1000; // 10分钟

    for (const [key, item] of this.nodeCache.entries()) {
      if (now - item.timestamp > maxAge) {
        this.nodeCache.delete(key);
      }
    }

    this.emit('cacheCleanup', { 
      remainingItems: this.nodeCache.size,
      maxSize: this.options.maxCacheSize
    });
  }

  /**
   * 检查是否应该执行节点
   */
  private shouldExecuteNode(node: Node): boolean {
    // 懒加载逻辑：只有当节点的输出被需要时才执行
    return node.hasConnectedOutputs();
  }

  /**
   * 检查是否应该缓存结果
   */
  private shouldCacheResult(node: Node, result: any): boolean {
    // 不缓存空结果或错误结果
    if (!result || result instanceof Error) {
      return false;
    }

    // 不缓存过大的结果
    const resultSize = JSON.stringify(result).length;
    if (resultSize > 1024 * 1024) { // 1MB
      return false;
    }

    return true;
  }

  /**
   * 获取节点依赖
   */
  private getNodeDependencies(node: Node, graph: Graph): Node[] {
    const dependencies: Node[] = [];
    const connections = graph.getConnections();

    for (const connection of connections) {
      if (connection.targetNodeId === node.id) {
        const sourceNode = graph.getNode(connection.sourceNodeId);
        if (sourceNode) {
          dependencies.push(sourceNode);
        }
      }
    }

    return dependencies;
  }

  /**
   * 查找入口节点
   */
  private findEntryNodes(nodes: Node[], graph: Graph): Node[] {
    const entryNodes: Node[] = [];
    const connections = graph.getConnections();
    const targetNodeIds = new Set(connections.map(c => c.targetNodeId));

    for (const node of nodes) {
      if (!targetNodeIds.has(node.id)) {
        entryNodes.push(node);
      }
    }

    return entryNodes;
  }

  /**
   * 检查内存阈值
   */
  private isMemoryThresholdExceeded(): boolean {
    const currentUsage = this.getCurrentMemoryUsage();
    return currentUsage > this.options.memoryThreshold;
  }

  /**
   * 获取当前内存使用
   */
  private getCurrentMemoryUsage(): number {
    if (typeof performance !== 'undefined' && (performance as any).memory) {
      return (performance as any).memory.usedJSHeapSize;
    }
    
    if (typeof process !== 'undefined' && process.memoryUsage) {
      return process.memoryUsage().heapUsed;
    }
    
    return 0;
  }

  /**
   * 执行垃圾回收
   */
  private async performGarbageCollection(): Promise<void> {
    // 清理缓存
    this.nodeCache.clear();
    
    // 清理性能分析数据
    this.profiler.clearProfiles();
    
    // 触发垃圾回收（如果可用）
    if (typeof global !== 'undefined' && global.gc) {
      global.gc();
    }

    this.emit('garbageCollected', { 
      memoryUsage: this.getCurrentMemoryUsage()
    });
  }

  /**
   * 重置统计
   */
  private resetStats(): void {
    this.stats = {
      totalNodes: 0,
      executedNodes: 0,
      cachedNodes: 0,
      parallelNodes: 0,
      totalExecutionTime: 0,
      averageExecutionTime: 0,
      memoryUsage: 0,
      cacheHitRate: 0
    };
  }

  /**
   * 获取执行统计
   */
  getStats(): ExecutionStats {
    return { ...this.stats };
  }

  /**
   * 获取缓存统计
   */
  getCacheStats(): any {
    return {
      size: this.nodeCache.size,
      maxSize: this.options.maxCacheSize,
      hitRate: this.stats.cacheHitRate,
      items: Array.from(this.nodeCache.values()).map(item => ({
        nodeId: item.nodeId,
        accessCount: item.accessCount,
        age: Date.now() - item.timestamp
      }))
    };
  }

  /**
   * 清空缓存
   */
  clearCache(): void {
    this.nodeCache.clear();
    this.emit('cacheCleared');
  }

  /**
   * 更新优化选项
   */
  updateOptions(options: Partial<OptimizationOptions>): void {
    this.options = { ...this.options, ...options };
    
    if (options.enableProfiling !== undefined) {
      if (options.enableProfiling) {
        this.profiler.enable();
      } else {
        this.profiler.disable();
      }
    }
  }

  /**
   * 销毁引擎
   */
  destroy(): void {
    this.clearCache();
    this.profiler.clearProfiles();
    this.removeAllListeners();
  }
}
