/**
 * AdvancedAnimationNodes.ts
 * 
 * 高级动画节点 - 提供IK、动画重定向、混合形状等高级动画功能
 */

import { Node } from '../nodes/Node';
import { FlowNode } from '../nodes/FlowNode';
import { FunctionNode } from '../nodes/FunctionNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import type { Entity } from '../../core/Entity';
import type { World } from '../../core/World';

// 动画状态枚举
export enum AnimationState {
  IDLE = 'idle',
  PLAYING = 'playing',
  PAUSED = 'paused',
  STOPPED = 'stopped',
  BLENDING = 'blending'
}

// IK求解器类型
export enum IKSolverType {
  CCD = 'ccd',           // 循环坐标下降
  FABRIK = 'fabrik',     // 前向和后向到达反向运动学
  JACOBIAN = 'jacobian', // 雅可比矩阵
  ANALYTICAL = 'analytical' // 解析解
}

// 动画混合模式
export enum BlendMode {
  OVERRIDE = 'override',
  ADDITIVE = 'additive',
  MULTIPLY = 'multiply',
  SCREEN = 'screen'
}

// 缓动类型
export enum EaseType {
  LINEAR = 'linear',
  EASE_IN = 'easeIn',
  EASE_OUT = 'easeOut',
  EASE_IN_OUT = 'easeInOut',
  BOUNCE = 'bounce',
  ELASTIC = 'elastic'
}

/**
 * IK求解器节点
 */
export class IKSolverNode extends FunctionNode {
  constructor(options: any) {
    super({
      id: options.id,
      type: 'animation/ik/ikSolver',
      metadata: options.metadata,
      graph: options.graph,
      context: options.context
    });
    
    // 设置节点类别
    (this as any).category = NodeCategory.ANIMATION;
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      description: '目标实体',
      defaultValue: null
    });
    
    this.addInput({
      name: 'targetPosition',
      type: SocketType.DATA,
      dataType: 'Vector3',
      description: '目标位置',
      defaultValue: { x: 0, y: 0, z: 0 }
    });
    
    this.addInput({
      name: 'chainLength',
      type: SocketType.DATA,
      dataType: 'number',
      description: '骨骼链长度',
      defaultValue: 3
    });
    
    this.addInput({
      name: 'iterations',
      type: SocketType.DATA,
      dataType: 'number',
      description: '迭代次数',
      defaultValue: 10
    });
    
    this.addInput({
      name: 'tolerance',
      type: SocketType.DATA,
      dataType: 'number',
      description: '容差',
      defaultValue: 0.01
    });

    this.addInput({
      name: 'solverType',
      type: SocketType.DATA,
      dataType: 'string',
      description: 'IK求解器类型',
      defaultValue: IKSolverType.FABRIK
    });
    
    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '求解成功'
    });
    
    this.addOutput({
      name: 'finalPosition',
      type: SocketType.DATA,
      dataType: 'Vector3',
      description: '最终位置'
    });
    
    this.addOutput({
      name: 'error',
      type: SocketType.DATA,
      dataType: 'number',
      description: '误差值'
    });

    this.addOutput({
      name: 'iterations',
      type: SocketType.DATA,
      dataType: 'number',
      description: '实际迭代次数'
    });
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const targetPosition = this.getInputValue('targetPosition') as { x: number, y: number, z: number };
    const chainLength = this.getInputValue('chainLength') as number;
    const iterations = this.getInputValue('iterations') as number;
    const tolerance = this.getInputValue('tolerance') as number;
    const solverType = this.getInputValue('solverType') as string;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有动画组件' };
      }

      // 获取IK系统
      const ikSystem = (animationComponent as any).getIKSystem?.() || (animationComponent as any).ikSystem;
      if (!ikSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: 'IK系统不可用' };
      }

      // 执行IK求解
      const result = ikSystem.solve({
        targetPosition,
        chainLength,
        iterations,
        tolerance,
        solverType
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('finalPosition', result.finalPosition || { x: 0, y: 0, z: 0 });
      this.setOutputValue('error', result.error || 0);
      this.setOutputValue('iterations', result.actualIterations || 0);
      
      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: (error as Error).message };
    }
  }
}

/**
 * IK目标节点
 */
export class IKTargetNode extends FunctionNode {
  constructor(options: any) {
    super({
      id: options.id,
      type: 'animation/ik/ikTarget',
      metadata: options.metadata,
      graph: options.graph,
      context: options.context
    });
    
    // 设置节点类别
    (this as any).category = NodeCategory.ANIMATION;
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      description: '目标实体',
      defaultValue: null
    });
    
    this.addInput({
      name: 'targetEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      description: '目标实体',
      defaultValue: null
    });
    
    this.addInput({
      name: 'boneName',
      type: SocketType.DATA,
      dataType: 'string',
      description: '骨骼名称',
      defaultValue: ''
    });
    
    this.addInput({
      name: 'weight',
      type: SocketType.DATA,
      dataType: 'number',
      description: '权重',
      defaultValue: 1.0
    });
    
    this.addInput({
      name: 'enabled',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '启用',
      defaultValue: true
    });
    
    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '设置成功'
    });
    
    this.addOutput({
      name: 'ikTarget',
      type: SocketType.DATA,
      dataType: 'object',
      description: 'IK目标对象'
    });
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const targetEntity = this.getInputValue('targetEntity') as Entity;
    const boneName = this.getInputValue('boneName') as string;
    const weight = this.getInputValue('weight') as number;
    const enabled = this.getInputValue('enabled') as boolean;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    if (!targetEntity) {
      this.setOutputValue('success', false);
      return { success: false, error: '目标实体不能为空' };
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有动画组件' };
      }

      // 获取IK系统
      const ikSystem = (animationComponent as any).getIKSystem?.() || (animationComponent as any).ikSystem;
      if (!ikSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: 'IK系统不可用' };
      }

      // 创建IK目标
      const ikTarget = ikSystem.createTarget({
        targetEntity,
        boneName,
        weight,
        enabled
      });

      this.setOutputValue('success', true);
      this.setOutputValue('ikTarget', ikTarget);
      
      return { success: true, ikTarget };
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: (error as Error).message };
    }
  }
}

/**
 * 动画重定向节点
 */
export class RetargetAnimationNode extends FunctionNode {
  constructor(options: any) {
    super({
      id: options.id,
      type: 'animation/retarget/retargetAnimation',
      metadata: options.metadata,
      graph: options.graph,
      context: options.context
    });

    // 设置节点类别
    (this as any).category = NodeCategory.ANIMATION;
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'sourceEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      description: '源实体',
      defaultValue: null
    });

    this.addInput({
      name: 'targetEntity',
      type: SocketType.DATA,
      dataType: 'Entity',
      description: '目标实体',
      defaultValue: null
    });

    this.addInput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'string',
      description: '动画片段',
      defaultValue: ''
    });

    this.addInput({
      name: 'boneMapping',
      type: SocketType.DATA,
      dataType: 'object',
      description: '骨骼映射',
      defaultValue: {}
    });

    this.addInput({
      name: 'scaleMode',
      type: SocketType.DATA,
      dataType: 'string',
      description: '缩放模式',
      defaultValue: 'proportional'
    });

    this.addInput({
      name: 'preserveRootMotion',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '保持根运动',
      defaultValue: true
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '重定向成功'
    });

    this.addOutput({
      name: 'retargetedClip',
      type: SocketType.DATA,
      dataType: 'object',
      description: '重定向后的动画片段'
    });

    this.addOutput({
      name: 'mappingReport',
      type: SocketType.DATA,
      dataType: 'object',
      description: '映射报告'
    });
  }

  protected executeImpl(): any {
    const sourceEntity = this.getInputValue('sourceEntity') as Entity;
    const targetEntity = this.getInputValue('targetEntity') as Entity;
    const animationClip = this.getInputValue('animationClip') as string;
    const boneMapping = this.getInputValue('boneMapping') as any;
    const scaleMode = this.getInputValue('scaleMode') as string;
    const preserveRootMotion = this.getInputValue('preserveRootMotion') as boolean;

    if (!sourceEntity || !targetEntity) {
      this.setOutputValue('success', false);
      return { success: false, error: '源实体和目标实体不能为空' };
    }

    try {
      // 获取动画组件
      const sourceAnimation = sourceEntity.getComponent('AnimationComponent');
      const targetAnimation = targetEntity.getComponent('AnimationComponent');

      if (!sourceAnimation || !targetAnimation) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体缺少动画组件' };
      }

      // 获取重定向系统
      const retargetSystem = (sourceAnimation as any).getRetargetSystem?.() || (sourceAnimation as any).retargetSystem;
      if (!retargetSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '重定向系统不可用' };
      }

      // 执行动画重定向
      const result = retargetSystem.retargetAnimation({
        sourceEntity,
        targetEntity,
        animationClip,
        boneMapping,
        scaleMode,
        preserveRootMotion
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('retargetedClip', result.retargetedClip);
      this.setOutputValue('mappingReport', result.mappingReport || {});

      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: (error as Error).message };
    }
  }
}

/**
 * 混合形状控制节点
 */
export class BlendShapeControlNode extends FunctionNode {
  constructor(options: any) {
    super({
      id: options.id,
      type: 'animation/blendshape/blendShapeControl',
      metadata: options.metadata,
      graph: options.graph,
      context: options.context
    });

    // 设置节点类别
    (this as any).category = NodeCategory.ANIMATION;
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      description: '目标实体',
      defaultValue: null
    });

    this.addInput({
      name: 'blendShapeName',
      type: SocketType.DATA,
      dataType: 'string',
      description: '混合形状名称',
      defaultValue: ''
    });

    this.addInput({
      name: 'weight',
      type: SocketType.DATA,
      dataType: 'number',
      description: '权重值',
      defaultValue: 0.0
    });

    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      description: '过渡时间',
      defaultValue: 0.0
    });

    this.addInput({
      name: 'easeType',
      type: SocketType.DATA,
      dataType: 'string',
      description: '缓动类型',
      defaultValue: EaseType.LINEAR
    });

    this.addInput({
      name: 'relative',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '相对权重',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '设置成功'
    });

    this.addOutput({
      name: 'currentWeight',
      type: SocketType.DATA,
      dataType: 'number',
      description: '当前权重'
    });

    this.addOutput({
      name: 'blendShapeId',
      type: SocketType.DATA,
      dataType: 'string',
      description: '混合形状ID'
    });
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const blendShapeName = this.getInputValue('blendShapeName') as string;
    const weight = this.getInputValue('weight') as number;
    const duration = this.getInputValue('duration') as number;
    const easeType = this.getInputValue('easeType') as string;
    const relative = this.getInputValue('relative') as boolean;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    if (!blendShapeName) {
      this.setOutputValue('success', false);
      return { success: false, error: '混合形状名称不能为空' };
    }

    try {
      // 获取渲染组件
      const renderComponent = entity.getComponent('RenderComponent');
      if (!renderComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有渲染组件' };
      }

      // 获取混合形状系统
      const blendShapeSystem = (renderComponent as any).getBlendShapeSystem?.() || (renderComponent as any).blendShapeSystem;
      if (!blendShapeSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '混合形状系统不可用' };
      }

      // 设置混合形状权重
      const result = blendShapeSystem.setBlendShapeWeight({
        name: blendShapeName,
        weight,
        duration,
        easeType,
        relative
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('currentWeight', result.currentWeight || 0.0);
      this.setOutputValue('blendShapeId', result.blendShapeId || '');

      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: (error as Error).message };
    }
  }
}

/**
 * 动画状态机节点
 */
export class AnimationStateMachineNode extends FunctionNode {
  constructor(options: any) {
    super({
      id: options.id,
      type: 'animation/statemachine/animationStateMachine',
      metadata: options.metadata,
      graph: options.graph,
      context: options.context
    });

    // 设置节点类别
    (this as any).category = NodeCategory.ANIMATION;
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      description: '目标实体',
      defaultValue: null
    });

    this.addInput({
      name: 'stateName',
      type: SocketType.DATA,
      dataType: 'string',
      description: '状态名称',
      defaultValue: ''
    });

    this.addInput({
      name: 'transitionDuration',
      type: SocketType.DATA,
      dataType: 'number',
      description: '过渡时间',
      defaultValue: 0.3
    });

    this.addInput({
      name: 'parameters',
      type: SocketType.DATA,
      dataType: 'object',
      description: '状态参数',
      defaultValue: {}
    });

    this.addInput({
      name: 'forceTransition',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '强制过渡',
      defaultValue: false
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '切换成功'
    });

    this.addOutput({
      name: 'currentState',
      type: SocketType.DATA,
      dataType: 'string',
      description: '当前状态'
    });

    this.addOutput({
      name: 'previousState',
      type: SocketType.DATA,
      dataType: 'string',
      description: '前一个状态'
    });

    this.addOutput({
      name: 'transitionProgress',
      type: SocketType.DATA,
      dataType: 'number',
      description: '过渡进度'
    });
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const stateName = this.getInputValue('stateName') as string;
    const transitionDuration = this.getInputValue('transitionDuration') as number;
    const parameters = this.getInputValue('parameters') as any;
    const forceTransition = this.getInputValue('forceTransition') as boolean;

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    if (!stateName) {
      this.setOutputValue('success', false);
      return { success: false, error: '状态名称不能为空' };
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有动画组件' };
      }

      // 获取状态机
      const stateMachine = (animationComponent as any).getStateMachine?.() || (animationComponent as any).stateMachine;
      if (!stateMachine) {
        this.setOutputValue('success', false);
        return { success: false, error: '动画状态机不可用' };
      }

      // 切换状态
      const result = stateMachine.transitionTo({
        stateName,
        transitionDuration,
        parameters,
        forceTransition
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('currentState', result.currentState || '');
      this.setOutputValue('previousState', result.previousState || '');
      this.setOutputValue('transitionProgress', result.transitionProgress || 0);

      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: (error as Error).message };
    }
  }
}

/**
 * 程序化动画生成节点
 */
export class ProceduralAnimationNode extends FunctionNode {
  constructor(options: any) {
    super({
      id: options.id,
      type: 'animation/procedural/proceduralAnimation',
      metadata: options.metadata,
      graph: options.graph,
      context: options.context
    });

    // 设置节点类别
    (this as any).category = NodeCategory.ANIMATION;
  }

  protected initializeSockets(): void {
    // 输入插槽
    this.addInput({
      name: 'entity',
      type: SocketType.DATA,
      dataType: 'Entity',
      description: '目标实体',
      defaultValue: null
    });

    this.addInput({
      name: 'animationType',
      type: SocketType.DATA,
      dataType: 'string',
      description: '动画类型',
      defaultValue: 'sine'
    });

    this.addInput({
      name: 'frequency',
      type: SocketType.DATA,
      dataType: 'number',
      description: '频率',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'amplitude',
      type: SocketType.DATA,
      dataType: 'number',
      description: '振幅',
      defaultValue: 1.0
    });

    this.addInput({
      name: 'phase',
      type: SocketType.DATA,
      dataType: 'number',
      description: '相位',
      defaultValue: 0.0
    });

    this.addInput({
      name: 'targetBones',
      type: SocketType.DATA,
      dataType: 'array',
      description: '目标骨骼',
      defaultValue: []
    });

    // 输出插槽
    this.addOutput({
      name: 'success',
      type: SocketType.DATA,
      dataType: 'boolean',
      description: '生成成功'
    });

    this.addOutput({
      name: 'animationClip',
      type: SocketType.DATA,
      dataType: 'object',
      description: '生成的动画片段'
    });

    this.addOutput({
      name: 'duration',
      type: SocketType.DATA,
      dataType: 'number',
      description: '动画时长'
    });
  }

  protected executeImpl(): any {
    const entity = this.getInputValue('entity') as Entity;
    const animationType = this.getInputValue('animationType') as string;
    const frequency = this.getInputValue('frequency') as number;
    const amplitude = this.getInputValue('amplitude') as number;
    const phase = this.getInputValue('phase') as number;
    const targetBones = this.getInputValue('targetBones') as string[];

    if (!entity) {
      this.setOutputValue('success', false);
      return { success: false, error: '实体不能为空' };
    }

    try {
      // 获取动画组件
      const animationComponent = entity.getComponent('AnimationComponent');
      if (!animationComponent) {
        this.setOutputValue('success', false);
        return { success: false, error: '实体没有动画组件' };
      }

      // 获取程序化动画系统
      const proceduralSystem = (animationComponent as any).getProceduralSystem?.() || (animationComponent as any).proceduralSystem;
      if (!proceduralSystem) {
        this.setOutputValue('success', false);
        return { success: false, error: '程序化动画系统不可用' };
      }

      // 生成程序化动画
      const result = proceduralSystem.generateAnimation({
        animationType,
        frequency,
        amplitude,
        phase,
        targetBones
      });

      this.setOutputValue('success', result.success);
      this.setOutputValue('animationClip', result.animationClip);
      this.setOutputValue('duration', result.duration || 0);

      return result;
    } catch (error) {
      this.setOutputValue('success', false);
      return { success: false, error: (error as Error).message };
    }
  }
}

/**
 * 注册高级动画节点
 */
export function registerAdvancedAnimationNodes(registry: NodeRegistry): void {
  // IK系统节点
  registry.registerNodeType({
    type: 'animation/ik/ikSolver',
    category: NodeCategory.ANIMATION,
    constructor: IKSolverNode,
    label: 'IK求解器',
    description: '反向动力学求解器，支持多种求解算法',
    tags: ['animation', 'ik', 'solver', 'advanced'],
    version: '2.0.0'
  });

  registry.registerNodeType({
    type: 'animation/ik/ikTarget',
    category: NodeCategory.ANIMATION,
    constructor: IKTargetNode,
    label: 'IK目标',
    description: '设置IK目标，支持动态目标跟踪',
    tags: ['animation', 'ik', 'target', 'advanced'],
    version: '2.0.0'
  });

  // 动画重定向节点
  registry.registerNodeType({
    type: 'animation/retarget/retargetAnimation',
    category: NodeCategory.ANIMATION,
    constructor: RetargetAnimationNode,
    label: '动画重定向',
    description: '将动画从一个骨架重定向到另一个骨架，支持自动骨骼映射',
    tags: ['animation', 'retarget', 'advanced'],
    version: '2.0.0'
  });

  // 混合形状节点
  registry.registerNodeType({
    type: 'animation/blendshape/blendShapeControl',
    category: NodeCategory.ANIMATION,
    constructor: BlendShapeControlNode,
    label: '混合形状控制',
    description: '控制混合形状权重，支持平滑过渡和相对权重',
    tags: ['animation', 'blendshape', 'morph', 'advanced'],
    version: '2.0.0'
  });

  // 动画状态机节点
  registry.registerNodeType({
    type: 'animation/statemachine/animationStateMachine',
    category: NodeCategory.ANIMATION,
    constructor: AnimationStateMachineNode,
    label: '动画状态机',
    description: '管理动画状态转换，支持参数化状态控制',
    tags: ['animation', 'statemachine', 'state', 'advanced'],
    version: '2.0.0'
  });

  // 程序化动画节点
  registry.registerNodeType({
    type: 'animation/procedural/proceduralAnimation',
    category: NodeCategory.ANIMATION,
    constructor: ProceduralAnimationNode,
    label: '程序化动画',
    description: '生成程序化动画，支持数学函数驱动的动画',
    tags: ['animation', 'procedural', 'generated', 'advanced'],
    version: '2.0.0'
  });

  console.log('已注册所有高级动画节点类型');
}
